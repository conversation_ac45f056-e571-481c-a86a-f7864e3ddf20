/**
 * Database configuration for the backend application.
 * The connection logic expects a postgreSQL database.
 */

import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;

// TODO - Configure database connection settings


const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Test connection
pool.on('connect', () => {
  console.log('Connected to PostgreSQL database: ', process.env.DB_NAME);
});

pool.on('error', (err) => {
  console.error('Database connection error:', err);
});

export default pool;
