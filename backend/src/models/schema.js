/**
 * Database Schema Initialization
 * Creates all necessary tables for the task management system
 */

import pool from '../config/database.js';

// SQL for creating users table
const createUsersTable = `
  CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'user')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
`;

// SQL for creating tasks table
const createTasksTable = `
  CREATE TABLE IF NOT EXISTS tasks (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    due_date TIMESTAMP WITH TIME ZONE,
    created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assigned_to INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
`;

// SQL for creating indexes for better performance
const createIndexes = `
  CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
  CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
  CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
  CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
  CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority);
  CREATE INDEX IF NOT EXISTS idx_tasks_created_by ON tasks(created_by);
  CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to);
  CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
`;

// SQL for creating triggers to automatically update updated_at timestamps
const createTriggers = `
  CREATE OR REPLACE FUNCTION update_updated_at_column()
  RETURNS TRIGGER AS $$
  BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
  END;
  $$ language 'plpgsql';

  DROP TRIGGER IF EXISTS update_users_updated_at ON users;
  CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

  DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
  CREATE TRIGGER update_tasks_updated_at
    BEFORE UPDATE ON tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
`;

// initialise database schema
export async function initialiseDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('Starting database initialization...');
    
    // Create tables
    console.log('Creating users table...');
    await client.query(createUsersTable);
    
    console.log('Creating tasks table...');
    await client.query(createTasksTable);
    
    // Create indexes
    console.log('Creating indexes...');
    await client.query(createIndexes);
    
    // Create triggers
    console.log('Creating triggers...');
    await client.query(createTriggers);
    
    console.log('Database initialization completed successfully!');
    return true;
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Drop all tables (for testing/reset purposes)
export async function dropAllTables() {
  const client = await pool.connect();
  
  try {
    console.log('Dropping all tables...');
    
    await client.query('DROP TABLE IF EXISTS tasks CASCADE;');
    await client.query('DROP TABLE IF EXISTS users CASCADE;');
    await client.query('DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;');
    
    console.log('All tables dropped successfully!');
    return true;
  } catch (error) {
    console.error('Failed to drop tables:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Check if database is properly initialised
export async function checkDatabaseStatus() {
  const client = await pool.connect();
  
  try {
    // Check if tables exist
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'tasks')
      ORDER BY table_name;
    `;
    
    const result = await client.query(tablesQuery);
    const existingTables = result.rows.map(row => row.table_name);
    
    const requiredTables = ['tasks', 'users'];
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    return {
      isinitialised: missingTables.length === 0,
      existingTables,
      missingTables
    };
  } catch (error) {
    console.error('Failed to check database status:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Seed database with sample data for testing
export async function seedDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('Seeding database with sample data...');
    
    // Create sample users
    const usersQuery = `
      INSERT INTO users (email, password, first_name, last_name, role) VALUES
      ('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Admin', 'User', 'admin'),
      ('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Manager', 'User', 'manager'),
      ('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Regular', 'User', 'user')
      ON CONFLICT (email) DO NOTHING
      RETURNING id, email, role;
    `;
    
    const userResult = await client.query(usersQuery);
    console.log('Sample users created:', userResult.rows);
    
    // Get user IDs for task creation
    const getUsersQuery = 'SELECT id, role FROM users ORDER BY id LIMIT 3';
    const users = await client.query(getUsersQuery);
    
    if (users.rows.length >= 3) {
      const [admin, manager, user] = users.rows;
      
      // Create sample tasks
      const tasksQuery = `
        INSERT INTO tasks (title, description, status, priority, created_by, assigned_to, due_date) VALUES
        ('Setup Project Environment', 'initialise the development environment and configure all necessary tools', 'completed', 'high', $1, $2, NOW() + INTERVAL '7 days'),
        ('Design Database Schema', 'Create comprehensive database schema for the task management system', 'in_progress', 'high', $2, $2, NOW() + INTERVAL '5 days'),
        ('Implement User Authentication', 'Build secure user authentication system with JWT tokens', 'pending', 'medium', $2, $3, NOW() + INTERVAL '10 days'),
        ('Create API Documentation', 'Document all API endpoints with examples and response formats', 'pending', 'low', $1, $2, NOW() + INTERVAL '14 days')
        ON CONFLICT DO NOTHING
        RETURNING id, title, status;
      `;
      
      const taskResult = await client.query(tasksQuery, [admin.id, manager.id, user.id]);
      console.log('Sample tasks created:', taskResult.rows);
    }
    
    console.log('Database seeding completed successfully!');
    return true;
  } catch (error) {
    console.error('Database seeding failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

export default {
  initialiseDatabase,
  dropAllTables,
  checkDatabaseStatus,
  seedDatabase
};
