/**
 * Task management routes
 * Handles all API endpoints related to task management
 * CRUD operations for tasks with role-based access control
 */
import express from 'express';
import Joi from 'joi';
import Task from '../models/Task.js';
import User from '../models/User.js';

const router = express.Router();

// Validation schemas
const createTaskSchema = Joi.object({
  title: Joi.string().min(3).max(255).required(),
  description: Joi.string().max(2000),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent').default('medium'),
  dueDate: Joi.date().iso().min('now'),
  assignedTo: Joi.number().integer().positive()
});

const updateTaskSchema = Joi.object({
  title: Joi.string().min(3).max(255),
  description: Joi.string().max(2000),
  status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled'),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent'),
  dueDate: Joi.date().iso(),
  assignedTo: Joi.number().integer().positive().allow(null)
}).min(1);

const statusUpdateSchema = Joi.object({
  status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').required()
});

// Middleware to simulate authentication (for testing purposes)
// In production, replace this with proper JWT authentication
const mockAuth = (req, res, next) => {
  // For testing, we'll use query parameters to simulate user authentication
  const userId = parseInt(req.query.userId || req.headers['x-user-id']);
  const userRole = req.query.userRole || req.headers['x-user-role'] || 'user';

  if (!userId) {
    return res.status(401).json({
      error: 'Authentication required. Provide userId in query params or x-user-id header for testing.'
    });
  }

  req.user = { id: userId, role: userRole };
  next();
};

// GET /api/tasks - Retrieve all tasks based on user permissions
router.get('/', mockAuth, async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return res.status(400).json({
        error: 'Invalid pagination parameters. Page must be >= 1, limit must be 1-100'
      });
    }

    // Build filters
    const filters = {};
    if (req.query.status) filters.status = req.query.status;
    if (req.query.priority) filters.priority = req.query.priority;
    if (req.query.assignedTo) filters.assignedTo = parseInt(req.query.assignedTo);
    if (req.query.createdBy) filters.createdBy = parseInt(req.query.createdBy);

    const result = await Task.findByUserRole(req.user.id, req.user.role, page, limit, filters);

    res.json({
      success: true,
      data: result.tasks,
      pagination: result.pagination
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/tasks - Create a new task in the system
router.post('/', mockAuth, async (req, res, next) => {
  try {
    // Only managers and admins can create tasks
    if (req.user.role === 'user') {
      return res.status(403).json({
        error: 'Insufficient permissions. Only managers and admins can create tasks.'
      });
    }

    // Validate request body
    const { error, value } = createTaskSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(d => d.message)
      });
    }

    // Verify assigned user exists if provided
    if (value.assignedTo) {
      const assignedUser = await User.findById(value.assignedTo);
      if (!assignedUser) {
        return res.status(400).json({ error: 'Assigned user does not exist' });
      }
    }

    // Set creator
    value.createdBy = req.user.id;

    const task = await Task.create(value);

    res.status(201).json({
      success: true,
      message: 'Task created successfully',
      data: task.toJSON()
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/tasks/:id - Retrieve a task by its ID
router.get('/:id', mockAuth, async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.id);

    if (isNaN(taskId)) {
      return res.status(400).json({ error: 'Invalid task ID' });
    }

    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    // Check permissions
    if (req.user.role === 'user' && task.assignedTo !== req.user.id) {
      return res.status(403).json({
        error: 'You can only view tasks assigned to you'
      });
    }

    if (req.user.role === 'manager' &&
        task.createdBy !== req.user.id &&
        task.assignedTo !== req.user.id) {
      return res.status(403).json({
        error: 'You can only view tasks you created or are assigned to'
      });
    }

    res.json({
      success: true,
      data: task.toJSON()
    });
  } catch (error) {
    next(error);
  }
});

// PATCH /api/tasks/:id - Update a task's details
router.patch('/:id', mockAuth, async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.id);

    if (isNaN(taskId)) {
      return res.status(400).json({ error: 'Invalid task ID' });
    }

    // Validate request body
    const { error, value } = updateTaskSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(d => d.message)
      });
    }

    // Verify assigned user exists if provided
    if (value.assignedTo) {
      const assignedUser = await User.findById(value.assignedTo);
      if (!assignedUser) {
        return res.status(400).json({ error: 'Assigned user does not exist' });
      }
    }

    const task = await Task.update(taskId, value, req.user.id, req.user.role);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json({
      success: true,
      message: 'Task updated successfully',
      data: task.toJSON()
    });
  } catch (error) {
    if (error.message.includes('Insufficient permissions')) {
      return res.status(403).json({ error: error.message });
    }
    next(error);
  }
});

// DELETE /api/tasks/:id - Delete a task by its ID
router.delete('/:id', mockAuth, async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.id);

    if (isNaN(taskId)) {
      return res.status(400).json({ error: 'Invalid task ID' });
    }

    const deleted = await Task.delete(taskId, req.user.id, req.user.role);

    if (!deleted) {
      return res.status(404).json({ error: 'Task not found or insufficient permissions' });
    }

    res.json({
      success: true,
      message: 'Task deleted successfully'
    });
  } catch (error) {
    if (error.message.includes('Insufficient permissions')) {
      return res.status(403).json({ error: error.message });
    }
    next(error);
  }
});

// PATCH /api/tasks/:id/status - Update task status
router.patch('/:id/status', mockAuth, async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.id);

    if (isNaN(taskId)) {
      return res.status(400).json({ error: 'Invalid task ID' });
    }

    // Validate request body
    const { error, value } = statusUpdateSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(d => d.message)
      });
    }

    const task = await Task.updateStatus(taskId, value.status, req.user.id, req.user.role);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json({
      success: true,
      message: 'Task status updated successfully',
      data: task.toJSON()
    });
  } catch (error) {
    if (error.message.includes('Insufficient permissions') ||
        error.message.includes('can only update status')) {
      return res.status(403).json({ error: error.message });
    }
    next(error);
  }
});

// POST /api/tasks/:id/assign - Assign task to a user
router.post('/:id/assign', mockAuth, async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.id);
    const { userId } = req.body;

    if (isNaN(taskId)) {
      return res.status(400).json({ error: 'Invalid task ID' });
    }

    if (!userId || isNaN(parseInt(userId))) {
      return res.status(400).json({ error: 'Valid userId is required' });
    }

    // Only managers and admins can assign tasks
    if (req.user.role === 'user') {
      return res.status(403).json({
        error: 'Insufficient permissions. Only managers and admins can assign tasks.'
      });
    }

    // Verify assigned user exists
    const assignedUser = await User.findById(parseInt(userId));
    if (!assignedUser) {
      return res.status(400).json({ error: 'User to assign does not exist' });
    }

    const task = await Task.update(taskId, { assignedTo: parseInt(userId) }, req.user.id, req.user.role);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json({
      success: true,
      message: 'Task assigned successfully',
      data: task.toJSON()
    });
  } catch (error) {
    if (error.message.includes('Insufficient permissions')) {
      return res.status(403).json({ error: error.message });
    }
    next(error);
  }
});

// DELETE /api/tasks/:id/assign - Unassign task
router.delete('/:id/assign', mockAuth, async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.id);

    if (isNaN(taskId)) {
      return res.status(400).json({ error: 'Invalid task ID' });
    }

    // Only managers and admins can unassign tasks
    if (req.user.role === 'user') {
      return res.status(403).json({
        error: 'Insufficient permissions. Only managers and admins can unassign tasks.'
      });
    }

    const task = await Task.update(taskId, { assignedTo: null }, req.user.id, req.user.role);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json({
      success: true,
      message: 'Task unassigned successfully',
      data: task.toJSON()
    });
  } catch (error) {
    if (error.message.includes('Insufficient permissions')) {
      return res.status(403).json({ error: error.message });
    }
    next(error);
  }
});

export default router;
